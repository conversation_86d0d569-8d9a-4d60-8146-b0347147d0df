import { Visibility } from "@mui/icons-material";
import { Box, Button, Grid2, IconButton, Tooltip } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { setFullviewMode } from "src/store/slices/app.slice";
import { z } from "zod";
import { useContentHeight } from "../../../../customHooks/useContentHeight";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";
import payrollService from "../../../../services/payroll.service";
import { useAppForm } from "../../../Common/Form/effiFormContext";
import CompensationComponentTypes from "./CompensationComponentTypes";
import DynamicPayrollInputs from "./DynamicPayrollInputs";
import FixedPayrollInputs from "./FixedPayrollInputs";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";

type Props = {
  isEdit: boolean;
};

export type DefaultFixedFormState = {
  name: string;
  country: string;
  jobTitles: string[];
};

const formSchema = z.object({
  id: z.string().nullish(),
  name: z.string().nonempty({
    message: "Name is required",
  }),
  description: z.string(),
  country: z.string().nonempty({
    message: "Country is required",
  }),
  job_titles: z.array(z.string()).nonempty({
    message: "Job Titles are required",
  }),
  employee_types: z.array(z.string()).nonempty({
    message: "Employee Types are required",
  }),
  aggregates: z.object({
    type: z.enum(["CTC", "GROSS"]),
    value: z.number(),
  }),
  components: z.array(
    z.object({
      id: z.string().nullish(),
      component_name: z.string(),
      component_value: z.number().or(z.string()),
    }),
  ),
});

export const getTemplateDefaultFormState = (selectedRow?: PayrollTemplateV2 | null): DefaultFixedFormState | any => {
  return {
    name: selectedRow?.name || "",
    description: selectedRow?.description || "",
    country: selectedRow?.country || "",
    job_titles: selectedRow?.job_titles || "",
    employee_types: selectedRow?.employee_types || "",
    aggregates: {
      type: "CTC",
      value: 0,
    },
    components:
      selectedRow?.components?.map((eachComponent: PayrollComponentV2) => ({
        ...eachComponent,
        component_name: eachComponent.name,
        component_value: eachComponent.formula.value,
        name: eachComponent.name,
        formula: {
          ...eachComponent.formula,
        },
      })) || [],
  };
};

const AddEditTemplate: React.FC<PayrollTemplateProps & Props> = ({
  isEdit = false,
  selectedRow,
  setCurrentSelectedMode,
  setSelectedRow,
}) => {
  const height = useContentHeight();
  const dispatch = useAppDispatch();
  const [selectedComponentTypes, setSelectedComponentTypes] = React.useState({});

  const form = useAppForm({
    defaultValues: getTemplateDefaultFormState(selectedRow),
    validators: {
      // onChange: formSchema as any,
      onSubmit: formSchema,
    },
    onSubmit: (params: any) => {
      const formValues = params.value;
      const requestStructure: Partial<PayrollTemplateV2> = {
        name: formValues.name,
        description: formValues.description,
        country: formValues.country,
        job_titles: formValues.job_titles,
        employee_types: formValues.employee_types,
        components: formValues.components.map((eachComponent: any, idx: number) => {
          const componentFromCompanyType = allComponents?.find(
            (_eachComponent) => _eachComponent.name === eachComponent.component_name,
          );
          return {
            ...componentFromCompanyType,
            sort_order: idx,
            formula: {
              ...componentFromCompanyType?.formula,
              value: eachComponent?.component_value,
            },
          };
        }),
      };
      if (isEdit) {
        updateTemplate.mutate(requestStructure as PayrollTemplateV2);
        return;
      }
      createTemplate.mutate(requestStructure as PayrollTemplateV2);
    },
  });
  const country = useStore(form.store, (state) => state.values.country);

  const { data: allComponents } = useQuery(
    ["get-all-components", country, selectedRow?.name],
    async () => {
      const allCompensationComponents: PayrollComponentV2[] =
        await payrollService.getAllCompensationComponents(country);

      const mandatoryComponents =
        allCompensationComponents?.filter((eachComponent: PayrollComponentV2) => eachComponent.mandatory) || [];

      // If editing a template, ensure all existing components are available
      let existingTemplateComponents: PayrollComponentV2[] = [];
      if (selectedRow?.components) {
        existingTemplateComponents = selectedRow.components.filter(
          (existingComponent) =>
            !mandatoryComponents.some((mandatoryComponent) => mandatoryComponent.name === existingComponent.name),
        );
      }

      const componentsToSetInState = [...mandatoryComponents, ...existingTemplateComponents];

      const componentsByType = componentsToSetInState.reduce(
        (acc: Record<string, PayrollComponentV2[]>, eachComponent) => {
          if (!acc[eachComponent.component_type]) {
            acc[eachComponent.component_type] = [];
          }
          acc[eachComponent.component_type].push(eachComponent);
          return acc;
        },
        {},
      );

      setSelectedComponentTypes((prev: Record<string, PayrollComponentV2[]>) => ({
        ...prev,
        ...componentsByType,
      }));

      return allCompensationComponents;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      enabled: !!country,
    },
  );

  const createTemplate = useMutation({
    mutationKey: ["create-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.createTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updateTemplate = useMutation({
    mutationKey: ["update-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.updateTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const goBack = () => {
    setCurrentSelectedMode(PayrollViewModes.VIEW_ALL);
    setSelectedComponentTypes({});
    dispatch(setFullviewMode(false));
    if (setSelectedRow) {
      setSelectedRow(null);
    }
  };

  useEffect(() => {
    dispatch(setFullviewMode(true));
    if (selectedRow?.components) {
      const defaultSelectedComponentTypes = selectedRow?.components.reduce(
        (acc: Record<string, PayrollComponentV2[]>, eachComponent: PayrollComponentV2) => {
          if (acc[eachComponent.component_type]) {
            const existingComponentTypes = acc[eachComponent.component_type];
            existingComponentTypes.push(eachComponent);
            acc[eachComponent.component_type] = existingComponentTypes;
            return acc;
          }
          acc[eachComponent.component_type] = [eachComponent];
          return acc;
        },
        {},
      );

      setSelectedComponentTypes(defaultSelectedComponentTypes);
    }
  }, [selectedRow]);

  console.log({ selectedComponentTypes });
  

  return (
    <Box>
      <ContentHeader title={isEdit ? "Edit Template" : "Add Template"} showBackButton goBack={goBack} />
      <Grid2 container spacing={2}>
        <Grid2 size={3}>
          <CompensationComponentTypes
            allComponents={allComponents}
            selectedComponentTypes={selectedComponentTypes}
            setSelectedComponentTypes={setSelectedComponentTypes}
          />
        </Grid2>
        <Grid2 size={9}>
          <Box display="flex" flexDirection="column" gap={1} overflow="hidden">
            <ContentHeader
              title="Details"
              actions={
                <Box display="flex" alignItems="center" gap={1}>
                  <Button variant="outlined">Preview</Button>
                  <form.Subscribe
                    selector={(state) => [
                      state.canSubmit,
                      state.isSubmitting,
                      state.isPristine,
                      state.isValid,
                      state.errorMap,
                    ]}
                  >
                    {([canSubmit, isSubmitting, isPristine]) => (
                      <Box>
                        <Button
                          sx={{ align: "right" }}
                          variant="contained"
                          disabled={!canSubmit || (isSubmitting as boolean) || (isPristine as boolean)}
                          onClick={form.handleSubmit}
                        >
                          Save
                        </Button>
                      </Box>
                    )}
                  </form.Subscribe>
                </Box>
              }
            />
            <Box sx={{ maxHeight: height - 120, overflow: "auto" }} display="flex" flexDirection="column" gap={3}>
              <FixedPayrollInputs form={form} isEdit={isEdit} />
              <DynamicPayrollInputs
                allComponents={allComponents}
                form={form}
                setSelectedComponentTypes={setSelectedComponentTypes}
                selectedRow={selectedRow as PayrollTemplateV2}
                selectedComponentTypes={selectedComponentTypes}
                isEdit={isEdit}
              />
            </Box>
          </Box>
        </Grid2>
      </Grid2>
    </Box>
  );
};

export default AddEditTemplate;
