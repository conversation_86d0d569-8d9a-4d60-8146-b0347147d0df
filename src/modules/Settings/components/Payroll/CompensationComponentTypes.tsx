import { Add, ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary, IconButton, Paper, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React, { useMemo } from "react";
import { useContentHeight } from "../../../../customHooks/useContentHeight";
import { useMasterData } from "../../../../customHooks/useMasterData";
import { PayrollComponentV2 } from "../../../../services/api_definitions/payroll.service";
import ContentHeader from "../../../Common/ContentHeader/ContentHeader";

type Props = {
  selectedComponentTypes: any;
  setSelectedComponentTypes: (selectedComponentTypes: any) => void;
  allComponents?: PayrollComponentV2[];
};

const CompensationComponentTypes: React.FC<Props> = ({
  allComponents,
  selectedComponentTypes,
  setSelectedComponentTypes,
}) => {
  const height = useContentHeight();
  const { data: componentTypes = [], isLoading: isComponentTypeLoading } = useMasterData("CompensationComponentType");
  const componentsByComponentType = useMemo(() => {
    return allComponents?.reduce((acc, eachComponent) => {
      if (acc.has(eachComponent.component_type)) {
        acc.get(eachComponent.component_type)?.push(eachComponent);
        return acc;
      }
      acc.set(eachComponent.component_type, [eachComponent]);
      return acc;
    }, new Map());
  }, [allComponents]);

  const onAddComponentClick = (componentType: string, component: PayrollComponentV2) => {    
    const selectedComponentTypeComponents = selectedComponentTypes[componentType] || [];
    selectedComponentTypeComponents.push(component);
    setSelectedComponentTypes((prev: any) => ({
      ...prev,
      [componentType]: selectedComponentTypeComponents,
    }));
  };

  const getComponentsToDisplay = (componentType: string) => {
    return componentsByComponentType?.get(componentType)?.filter((eachComponent: PayrollComponentV2) => {
      const elementsToFilterOut = selectedComponentTypes?.[eachComponent.component_type]?.map(
        (eachSelectedComponent: PayrollComponentV2) => eachSelectedComponent.name,
      );
      return !elementsToFilterOut?.includes(eachComponent.name);
    });
  };

  return (
    <Paper elevation={2} sx={{ height: height - 64, maxHeight: height, overflow: "auto", padding: 2 }}>
      <ContentHeader title="Component Library" subtitle="Build your pay templates using active components below." />
      {componentTypes &&
        !isComponentTypeLoading &&
        componentTypes.map((eachComponentType: any) => (
          <Accordion key={eachComponentType} sx={{ marginTop: "10px" }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>{eachComponentType}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {!allComponents && <Typography>No components found</Typography>}
              {getComponentsToDisplay(eachComponentType)?.map((eachComponent: any) => (
                <Box component={Paper} key={eachComponent.id} elevation={2} gap={1} margin="8px 0px" padding="4px 8px">
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Typography variant="body2">{eachComponent.name}</Typography>
                    <IconButton color="primary" onClick={() => onAddComponentClick(eachComponentType, eachComponent)}>
                      <Add />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </AccordionDetails>
          </Accordion>
        ))}
    </Paper>
  );
};
export default CompensationComponentTypes;
